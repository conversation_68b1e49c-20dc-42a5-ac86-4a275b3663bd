{"version": "0.2.0", "configurations": [{"name": "C++ Launch (g++-10)", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/helloworld", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": false, "MIMode": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "build hello world", "miDebuggerPath": "/usr/bin/gdb"}]}