{"artifacts": [{"path": "arrayAdder"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_compile_options"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 16, "parent": 0}, {"command": 1, "file": 0, "line": 24, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g"}, {"backtrace": 2, "fragment": "-O2"}, {"backtrace": 2, "fragment": "-Wall"}, {"fragment": "-std=gnu++17"}], "language": "CXX", "sourceIndexes": [0]}], "id": "arrayAdder::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"fragment": "-l<PERSON><PERSON><PERSON>", "role": "libraries"}], "language": "CXX"}, "name": "arrayAdder", "nameOnDisk": "arrayAdder", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "arrayAdder.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}