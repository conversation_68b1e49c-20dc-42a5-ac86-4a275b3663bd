{"configurations": [{"directories": [{"build": ".", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "ArrayAdder", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "arrayAdder::@6890427a1f51a3e7e1df", "jsonFile": "target-arrayAdder-Debug-d43567eca32c2c1fb6f8.json", "name": "arrayAdder", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/workspaces/cpp/build", "source": "/workspaces/cpp/GPUPractice"}, "version": {"major": 2, "minor": 1}}