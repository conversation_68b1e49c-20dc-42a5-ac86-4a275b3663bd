Performing C SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: /workspaces/cpp/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/ninja cmTC_c9940 && [1/2] Building C object CMakeFiles/cmTC_c9940.dir/src.c.o
[2/2] Linking C executable cmTC_c9940
FAILED: cmTC_c9940 
: && /usr/bin/gcc -DCMAKE_HAVE_LIBC_PTHREAD  CMakeFiles/cmTC_c9940.dir/src.c.o -o cmTC_c9940   && :
/usr/bin/ld: CMakeFiles/cmTC_c9940.dir/src.c.o: in function `main':
src.c:(.text+0x2f): undefined reference to `pthread_create'
/usr/bin/ld: src.c:(.text+0x3b): undefined reference to `pthread_detach'
/usr/bin/ld: src.c:(.text+0x47): undefined reference to `pthread_cancel'
/usr/bin/ld: src.c:(.text+0x58): undefined reference to `pthread_join'
collect2: error: ld returned 1 exit status
ninja: build stopped: subcommand failed.


Source file was:
#include <pthread.h>

void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_cancel(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}

Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: /workspaces/cpp/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/ninja cmTC_134f3 && [1/2] Building C object CMakeFiles/cmTC_134f3.dir/CheckFunctionExists.c.o
[2/2] Linking C executable cmTC_134f3
FAILED: cmTC_134f3 
: && /usr/bin/gcc -DCHECK_FUNCTION_EXISTS=pthread_create  CMakeFiles/cmTC_134f3.dir/CheckFunctionExists.c.o -o cmTC_134f3  -lpthreads && :
/usr/bin/ld: cannot find -lpthreads
collect2: error: ld returned 1 exit status
ninja: build stopped: subcommand failed.



