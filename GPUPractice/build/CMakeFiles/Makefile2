# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.18

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Disable VCS-based implicit rules.
% : %,v


# Disable VCS-based implicit rules.
% : RCS/%


# Disable VCS-based implicit rules.
% : RCS/%,v


# Disable VCS-based implicit rules.
% : SCCS/s.%


# Disable VCS-based implicit rules.
% : s.%


.SUFFIXES: .hpux_make_needs_suffix_list


# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /workspaces/cpp/GPUPractice

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /workspaces/cpp/GPUPractice/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/arrayAdderOpenCL.dir/all
all: CMakeFiles/arrayAdder.dir/all

.PHONY : all

# The main recursive "preinstall" target.
preinstall:

.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/arrayAdderOpenCL.dir/clean
clean: CMakeFiles/arrayAdder.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/arrayAdderOpenCL.dir

# All Build rule for target.
CMakeFiles/arrayAdderOpenCL.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/arrayAdderOpenCL.dir/build.make CMakeFiles/arrayAdderOpenCL.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/arrayAdderOpenCL.dir/build.make CMakeFiles/arrayAdderOpenCL.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/workspaces/cpp/GPUPractice/build/CMakeFiles --progress-num=3,4 "Built target arrayAdderOpenCL"
.PHONY : CMakeFiles/arrayAdderOpenCL.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/arrayAdderOpenCL.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /workspaces/cpp/GPUPractice/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/arrayAdderOpenCL.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /workspaces/cpp/GPUPractice/build/CMakeFiles 0
.PHONY : CMakeFiles/arrayAdderOpenCL.dir/rule

# Convenience name for target.
arrayAdderOpenCL: CMakeFiles/arrayAdderOpenCL.dir/rule

.PHONY : arrayAdderOpenCL

# clean rule for target.
CMakeFiles/arrayAdderOpenCL.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/arrayAdderOpenCL.dir/build.make CMakeFiles/arrayAdderOpenCL.dir/clean
.PHONY : CMakeFiles/arrayAdderOpenCL.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/arrayAdder.dir

# All Build rule for target.
CMakeFiles/arrayAdder.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/arrayAdder.dir/build.make CMakeFiles/arrayAdder.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/arrayAdder.dir/build.make CMakeFiles/arrayAdder.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/workspaces/cpp/GPUPractice/build/CMakeFiles --progress-num=1,2 "Built target arrayAdder"
.PHONY : CMakeFiles/arrayAdder.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/arrayAdder.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /workspaces/cpp/GPUPractice/build/CMakeFiles 2
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/arrayAdder.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /workspaces/cpp/GPUPractice/build/CMakeFiles 0
.PHONY : CMakeFiles/arrayAdder.dir/rule

# Convenience name for target.
arrayAdder: CMakeFiles/arrayAdder.dir/rule

.PHONY : arrayAdder

# clean rule for target.
CMakeFiles/arrayAdder.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/arrayAdder.dir/build.make CMakeFiles/arrayAdder.dir/clean
.PHONY : CMakeFiles/arrayAdder.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

