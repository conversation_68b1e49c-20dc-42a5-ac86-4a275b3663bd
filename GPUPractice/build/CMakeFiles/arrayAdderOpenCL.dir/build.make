# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.18

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Disable VCS-based implicit rules.
% : %,v


# Disable VCS-based implicit rules.
% : RCS/%


# Disable VCS-based implicit rules.
% : RCS/%,v


# Disable VCS-based implicit rules.
% : SCCS/s.%


# Disable VCS-based implicit rules.
% : s.%


.SUFFIXES: .hpux_make_needs_suffix_list


# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /workspaces/cpp/GPUPractice

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /workspaces/cpp/GPUPractice/build

# Include any dependencies generated for this target.
include CMakeFiles/arrayAdderOpenCL.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/arrayAdderOpenCL.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/arrayAdderOpenCL.dir/flags.make

CMakeFiles/arrayAdderOpenCL.dir/arrayAdderOpenCL.cpp.o: CMakeFiles/arrayAdderOpenCL.dir/flags.make
CMakeFiles/arrayAdderOpenCL.dir/arrayAdderOpenCL.cpp.o: ../arrayAdderOpenCL.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/workspaces/cpp/GPUPractice/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/arrayAdderOpenCL.dir/arrayAdderOpenCL.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/arrayAdderOpenCL.dir/arrayAdderOpenCL.cpp.o -c /workspaces/cpp/GPUPractice/arrayAdderOpenCL.cpp

CMakeFiles/arrayAdderOpenCL.dir/arrayAdderOpenCL.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/arrayAdderOpenCL.dir/arrayAdderOpenCL.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /workspaces/cpp/GPUPractice/arrayAdderOpenCL.cpp > CMakeFiles/arrayAdderOpenCL.dir/arrayAdderOpenCL.cpp.i

CMakeFiles/arrayAdderOpenCL.dir/arrayAdderOpenCL.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/arrayAdderOpenCL.dir/arrayAdderOpenCL.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /workspaces/cpp/GPUPractice/arrayAdderOpenCL.cpp -o CMakeFiles/arrayAdderOpenCL.dir/arrayAdderOpenCL.cpp.s

# Object files for target arrayAdderOpenCL
arrayAdderOpenCL_OBJECTS = \
"CMakeFiles/arrayAdderOpenCL.dir/arrayAdderOpenCL.cpp.o"

# External object files for target arrayAdderOpenCL
arrayAdderOpenCL_EXTERNAL_OBJECTS =

arrayAdderOpenCL: CMakeFiles/arrayAdderOpenCL.dir/arrayAdderOpenCL.cpp.o
arrayAdderOpenCL: CMakeFiles/arrayAdderOpenCL.dir/build.make
arrayAdderOpenCL: /usr/lib/x86_64-linux-gnu/libOpenCL.so
arrayAdderOpenCL: CMakeFiles/arrayAdderOpenCL.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/workspaces/cpp/GPUPractice/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable arrayAdderOpenCL"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/arrayAdderOpenCL.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/arrayAdderOpenCL.dir/build: arrayAdderOpenCL

.PHONY : CMakeFiles/arrayAdderOpenCL.dir/build

CMakeFiles/arrayAdderOpenCL.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/arrayAdderOpenCL.dir/cmake_clean.cmake
.PHONY : CMakeFiles/arrayAdderOpenCL.dir/clean

CMakeFiles/arrayAdderOpenCL.dir/depend:
	cd /workspaces/cpp/GPUPractice/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /workspaces/cpp/GPUPractice /workspaces/cpp/GPUPractice /workspaces/cpp/GPUPractice/build /workspaces/cpp/GPUPractice/build /workspaces/cpp/GPUPractice/build/CMakeFiles/arrayAdderOpenCL.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/arrayAdderOpenCL.dir/depend

