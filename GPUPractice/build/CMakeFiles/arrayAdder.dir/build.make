# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.18

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Disable VCS-based implicit rules.
% : %,v


# Disable VCS-based implicit rules.
% : RCS/%


# Disable VCS-based implicit rules.
% : RCS/%,v


# Disable VCS-based implicit rules.
% : SCCS/s.%


# Disable VCS-based implicit rules.
% : s.%


.SUFFIXES: .hpux_make_needs_suffix_list


# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /workspaces/cpp/GPUPractice

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /workspaces/cpp/GPUPractice/build

# Include any dependencies generated for this target.
include CMakeFiles/arrayAdder.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/arrayAdder.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/arrayAdder.dir/flags.make

CMakeFiles/arrayAdder.dir/arrayAdder.cpp.o: CMakeFiles/arrayAdder.dir/flags.make
CMakeFiles/arrayAdder.dir/arrayAdder.cpp.o: ../arrayAdder.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/workspaces/cpp/GPUPractice/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/arrayAdder.dir/arrayAdder.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/arrayAdder.dir/arrayAdder.cpp.o -c /workspaces/cpp/GPUPractice/arrayAdder.cpp

CMakeFiles/arrayAdder.dir/arrayAdder.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/arrayAdder.dir/arrayAdder.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /workspaces/cpp/GPUPractice/arrayAdder.cpp > CMakeFiles/arrayAdder.dir/arrayAdder.cpp.i

CMakeFiles/arrayAdder.dir/arrayAdder.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/arrayAdder.dir/arrayAdder.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /workspaces/cpp/GPUPractice/arrayAdder.cpp -o CMakeFiles/arrayAdder.dir/arrayAdder.cpp.s

# Object files for target arrayAdder
arrayAdder_OBJECTS = \
"CMakeFiles/arrayAdder.dir/arrayAdder.cpp.o"

# External object files for target arrayAdder
arrayAdder_EXTERNAL_OBJECTS =

arrayAdder: CMakeFiles/arrayAdder.dir/arrayAdder.cpp.o
arrayAdder: CMakeFiles/arrayAdder.dir/build.make
arrayAdder: CMakeFiles/arrayAdder.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/workspaces/cpp/GPUPractice/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable arrayAdder"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/arrayAdder.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/arrayAdder.dir/build: arrayAdder

.PHONY : CMakeFiles/arrayAdder.dir/build

CMakeFiles/arrayAdder.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/arrayAdder.dir/cmake_clean.cmake
.PHONY : CMakeFiles/arrayAdder.dir/clean

CMakeFiles/arrayAdder.dir/depend:
	cd /workspaces/cpp/GPUPractice/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /workspaces/cpp/GPUPractice /workspaces/cpp/GPUPractice /workspaces/cpp/GPUPractice/build /workspaces/cpp/GPUPractice/build /workspaces/cpp/GPUPractice/build/CMakeFiles/arrayAdder.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/arrayAdder.dir/depend

