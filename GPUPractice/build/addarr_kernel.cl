// OpenCL kernel for array addition
// This kernel performs element-wise addition of two float arrays

__kernel void addarr_kernel(__global const float* A, 
                           __global const float* B, 
                           __global float* C, 
                           const int N) {
    // Get the index of the current element to be processed
    int gid = get_global_id(0);
    
    // Make sure we don't go out of bounds
    if (gid < N) {
        C[gid] = A[gid] + B[gid];
    }
}

// Alternative kernel with local memory optimization for larger arrays
__kernel void addarr_kernel_optimized(__global const float* A, 
                                     __global const float* B, 
                                     __global float* C, 
                                     const int N,
                                     __local float* local_A,
                                     __local float* local_B) {
    int gid = get_global_id(0);
    int lid = get_local_id(0);
    int group_size = get_local_size(0);
    int group_id = get_group_id(0);
    
    // Calculate the starting index for this work group
    int group_start = group_id * group_size;
    
    // Load data into local memory
    if (group_start + lid < N) {
        local_A[lid] = A[group_start + lid];
        local_B[lid] = B[group_start + lid];
    }
    
    // Synchronize to make sure all data is loaded
    barrier(CLK_LOCAL_MEM_FENCE);
    
    // Perform the computation
    if (gid < N) {
        C[gid] = local_A[lid] + local_B[lid];
    }
}
