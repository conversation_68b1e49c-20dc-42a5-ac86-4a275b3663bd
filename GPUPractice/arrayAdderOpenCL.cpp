#include <iostream>
#include <cstdlib>
#include <array>
#include <random>
#include <ctime>
#include <chrono>
#include <thread>
#include <vector>
#include <fstream>
#include <sstream>

#define CL_TARGET_OPENCL_VERSION 120
#include <CL/cl.h>

// Function declarations
float *addarr(float A[], float B[], int N);
float *addarr_multithreaded(float A[], float B[], int N);
float *addarr_opencl(float A[], float B[], int N);
void addarr_worker(float A[], float B[], float C[], int start, int end);
float *generateRandomArray(int N, float min = 0.0f, float max = 10.0f);
void printArray(const char* label, float* arr, int N);
void testRandomArrays(int N);
float * timed(float* arr(float[], float[], int), float A[], float B[], int N, const char* method_name);

// OpenCL helper functions
std::string loadKernelSource(const std::string& filename);
void checkOpenCLError(cl_int error, const std::string& operation);
bool initializeOpenCL();
void cleanupOpenCL();

// Global OpenCL variables
cl_platform_id platform = nullptr;
cl_device_id device = nullptr;
cl_context context = nullptr;
cl_command_queue queue = nullptr;
cl_program program = nullptr;
cl_kernel kernel = nullptr;
bool opencl_initialized = false;

int main()
{
    std::cout << "\n--- Testing Random Arrays with OpenCL ---\n";
    
    // Initialize OpenCL
    if (initializeOpenCL()) {
        std::cout << "OpenCL initialized successfully!\n";
    } else {
        std::cout << "OpenCL initialization failed. Running CPU-only tests.\n";
    }
    
    for(int i = 2; i < 100000; i*=10) {  // Reduced max for testing
        std::cout << "\nIteration: " << i << " - ";
        testRandomArrays(i);
    }
    
    // Cleanup OpenCL
    cleanupOpenCL();
    
    return 0;
}

bool initializeOpenCL() {
    cl_int error;
    cl_uint num_platforms;
    
    // Get platform
    error = clGetPlatformIDs(1, &platform, &num_platforms);
    if (error != CL_SUCCESS || num_platforms == 0) {
        std::cerr << "Failed to get OpenCL platform" << std::endl;
        return false;
    }
    
    // Get device (prefer GPU, fallback to CPU)
    cl_uint num_devices;
    error = clGetDeviceIDs(platform, CL_DEVICE_TYPE_GPU, 1, &device, &num_devices);
    if (error != CL_SUCCESS || num_devices == 0) {
        std::cout << "No GPU found, trying CPU..." << std::endl;
        error = clGetDeviceIDs(platform, CL_DEVICE_TYPE_CPU, 1, &device, &num_devices);
        if (error != CL_SUCCESS || num_devices == 0) {
            std::cerr << "No OpenCL devices found" << std::endl;
            return false;
        }
    }
    
    // Print device info
    char device_name[256];
    clGetDeviceInfo(device, CL_DEVICE_NAME, sizeof(device_name), device_name, nullptr);
    std::cout << "Using OpenCL device: " << device_name << std::endl;
    
    // Create context
    context = clCreateContext(nullptr, 1, &device, nullptr, nullptr, &error);
    checkOpenCLError(error, "Creating context");
    
    // Create command queue
    queue = clCreateCommandQueue(context, device, 0, &error);
    checkOpenCLError(error, "Creating command queue");
    
    // Load and build kernel
    std::string kernel_source = loadKernelSource("addarr_kernel.cl");
    if (kernel_source.empty()) {
        std::cerr << "Failed to load kernel source" << std::endl;
        return false;
    }
    
    const char* source_ptr = kernel_source.c_str();
    size_t source_size = kernel_source.length();
    
    program = clCreateProgramWithSource(context, 1, &source_ptr, &source_size, &error);
    checkOpenCLError(error, "Creating program");
    
    error = clBuildProgram(program, 1, &device, nullptr, nullptr, nullptr);
    if (error != CL_SUCCESS) {
        // Get build log
        size_t log_size;
        clGetProgramBuildInfo(program, device, CL_PROGRAM_BUILD_LOG, 0, nullptr, &log_size);
        std::vector<char> log(log_size);
        clGetProgramBuildInfo(program, device, CL_PROGRAM_BUILD_LOG, log_size, log.data(), nullptr);
        std::cerr << "Build error: " << log.data() << std::endl;
        return false;
    }
    
    // Create kernel
    kernel = clCreateKernel(program, "addarr_kernel", &error);
    checkOpenCLError(error, "Creating kernel");
    
    opencl_initialized = true;
    return true;
}

void cleanupOpenCL() {
    if (kernel) clReleaseKernel(kernel);
    if (program) clReleaseProgram(program);
    if (queue) clReleaseCommandQueue(queue);
    if (context) clReleaseContext(context);
}

std::string loadKernelSource(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Failed to open kernel file: " << filename << std::endl;
        return "";
    }
    
    std::stringstream buffer;
    buffer << file.rdbuf();
    return buffer.str();
}

void checkOpenCLError(cl_int error, const std::string& operation) {
    if (error != CL_SUCCESS) {
        std::cerr << "OpenCL error in " << operation << ": " << error << std::endl;
        throw std::runtime_error("OpenCL error");
    }
}

float *addarr_opencl(float A[], float B[], int N) {
    if (!opencl_initialized) {
        std::cerr << "OpenCL not initialized, falling back to CPU" << std::endl;
        return addarr(A, B, N);
    }
    
    cl_int error;
    
    // Allocate result array
    float *C = (float *)malloc(N * sizeof(float));
    if (C == nullptr) {
        std::cerr << "Memory allocation failed!" << std::endl;
        return nullptr;
    }
    
    // Create buffers
    size_t buffer_size = N * sizeof(float);
    cl_mem buffer_A = clCreateBuffer(context, CL_MEM_READ_ONLY | CL_MEM_COPY_HOST_PTR, 
                                    buffer_size, A, &error);
    checkOpenCLError(error, "Creating buffer A");
    
    cl_mem buffer_B = clCreateBuffer(context, CL_MEM_READ_ONLY | CL_MEM_COPY_HOST_PTR, 
                                    buffer_size, B, &error);
    checkOpenCLError(error, "Creating buffer B");
    
    cl_mem buffer_C = clCreateBuffer(context, CL_MEM_WRITE_ONLY, buffer_size, nullptr, &error);
    checkOpenCLError(error, "Creating buffer C");
    
    // Set kernel arguments
    error = clSetKernelArg(kernel, 0, sizeof(cl_mem), &buffer_A);
    checkOpenCLError(error, "Setting kernel arg 0");
    
    error = clSetKernelArg(kernel, 1, sizeof(cl_mem), &buffer_B);
    checkOpenCLError(error, "Setting kernel arg 1");
    
    error = clSetKernelArg(kernel, 2, sizeof(cl_mem), &buffer_C);
    checkOpenCLError(error, "Setting kernel arg 2");
    
    error = clSetKernelArg(kernel, 3, sizeof(int), &N);
    checkOpenCLError(error, "Setting kernel arg 3");
    
    // Execute kernel
    size_t global_work_size = N;
    size_t local_work_size = 64;  // Adjust based on device capabilities
    
    // Round up global work size to be divisible by local work size
    if (global_work_size % local_work_size != 0) {
        global_work_size = ((global_work_size / local_work_size) + 1) * local_work_size;
    }
    
    error = clEnqueueNDRangeKernel(queue, kernel, 1, nullptr, &global_work_size, 
                                  &local_work_size, 0, nullptr, nullptr);
    checkOpenCLError(error, "Enqueuing kernel");
    
    // Read result
    error = clEnqueueReadBuffer(queue, buffer_C, CL_TRUE, 0, buffer_size, C, 0, nullptr, nullptr);
    checkOpenCLError(error, "Reading result buffer");
    
    // Cleanup buffers
    clReleaseMemObject(buffer_A);
    clReleaseMemObject(buffer_B);
    clReleaseMemObject(buffer_C);
    
    return C;
}

float *addarr(float A[], float B[], int N)
{
    float *C = (float *)malloc(N * sizeof(float));
    for (int i = 0; i < N; i++)
        C[i] = A[i] + B[i];
    return C;
}

// Worker function for multi-threaded array addition
void addarr_worker(float A[], float B[], float C[], int start, int end)
{
    for (int i = start; i < end; i++) {
        C[i] = A[i] + B[i];
    }
}

// Multi-threaded version of addarr
float *addarr_multithreaded(float A[], float B[], int N)
{
    // Allocate memory for result array
    float *C = (float *)malloc(N * sizeof(float));
    if (C == nullptr) {
        std::cerr << "Memory allocation failed in addarr_multithreaded!" << std::endl;
        return nullptr;
    }

    // Get number of hardware threads (cores)
    unsigned int num_threads = std::thread::hardware_concurrency();
    if (num_threads == 0) {
        num_threads = 4; // Default fallback if detection fails
    }

    // For small arrays, use single thread to avoid overhead
    if (N < num_threads * 100) {
        for (int i = 0; i < N; i++) {
            C[i] = A[i] + B[i];
        }
        return C;
    }

    // Calculate work distribution
    int elements_per_thread = N / num_threads;
    int remaining_elements = N % num_threads;

    // Create thread vector
    std::vector<std::thread> threads;
    threads.reserve(num_threads);

    // Launch threads
    int current_start = 0;
    for (unsigned int t = 0; t < num_threads; t++) {
        int current_end = current_start + elements_per_thread;

        // Give remaining elements to the last thread
        if (t == num_threads - 1) {
            current_end += remaining_elements;
        }

        threads.emplace_back(addarr_worker, A, B, C, current_start, current_end);
        current_start = current_end;
    }

    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }

    return C;
}

float *generateRandomArray(int N, float min, float max)
{
    // Seed the random number generator
    static bool seeded = false;
    if (!seeded) {
        std::srand(std::time(nullptr));
        seeded = true;
    }

    float *arr = (float *)malloc(N * sizeof(float));
    if (arr == nullptr) {
        std::cerr << "Memory allocation failed!" << std::endl;
        return nullptr;
    }

    for (int i = 0; i < N; i++) {
        // Generate random float between min and max
        float random = (float)std::rand() / RAND_MAX; // 0.0 to 1.0
        arr[i] = min + random * (max - min);
    }

    return arr;
}

void printArray(const char* label, float* arr, int N)
{
    // Only print for powers of 2 (and skip N=0 to avoid issues)
    if (N == 0 || (N & (N - 1)) != 0)
        return;
    std::cout << label << ": ";
    for (int i = 0; i < N; i++) {
        std::cout << arr[i] << " ";
    }
    std::cout << std::endl;
}

void testRandomArrays(int N=50)
{
    std::cout << "Testing random arrays of size " << N << std::endl;
    std::cout << "Generating two random arrays of size " << N << std::endl;

    float *A = generateRandomArray(N, 1.0f, 5.0f);
    float *B = generateRandomArray(N, 2.0f, 8.0f);

    if (A == nullptr || B == nullptr) {
        std::cerr << "Failed to generate random arrays!" << std::endl;
        return;
    }

    printArray("Array A", A, N);
    printArray("Array B", B, N);

    // Test single-threaded version
    std::cout << "Single-threaded: ";
    float *C_single = timed(addarr, A, B, N, "CPU Single");

    // Test multi-threaded version
    std::cout << "Multi-threaded:  ";
    float *C_multi = timed(addarr_multithreaded, A, B, N, "CPU Multi");

    // Test OpenCL version if available
    float *C_opencl = nullptr;
    if (opencl_initialized) {
        std::cout << "OpenCL GPU:      ";
        C_opencl = timed(addarr_opencl, A, B, N, "OpenCL GPU");
    }

    printArray("Sum A+B (single)", C_single, N);
    printArray("Sum A+B (multi)", C_multi, N);
    if (C_opencl) {
        printArray("Sum A+B (OpenCL)", C_opencl, N);
    }

    // Verify results are identical (for small arrays)
    if (N <= 16) {
        bool results_match = true;
        for (int i = 0; i < N; i++) {
            if (std::abs(C_single[i] - C_multi[i]) > 1e-6) {
                results_match = false;
                break;
            }
            if (C_opencl && std::abs(C_single[i] - C_opencl[i]) > 1e-6) {
                results_match = false;
                break;
            }
        }
        std::cout << "Results match: " << (results_match ? "YES" : "NO") << std::endl;
    }

    // Clean up memory
    free(A);
    free(B);
    free(C_single);
    free(C_multi);
    if (C_opencl) free(C_opencl);
}

float* timed(float* arr(float[], float[], int), float A[], float B[], int N, const char* method_name) {
    auto start = std::chrono::high_resolution_clock::now();
    float *C = arr(A, B, N);
    auto end = std::chrono::high_resolution_clock::now();

    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    std::cout << "Time: " << duration.count() << " microseconds (" << method_name << ")" << std::endl;

    return C;
}
